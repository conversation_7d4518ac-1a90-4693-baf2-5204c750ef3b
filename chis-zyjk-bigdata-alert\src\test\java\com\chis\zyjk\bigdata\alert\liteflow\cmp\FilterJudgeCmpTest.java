package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.project.frame.common.tools.json.JSONObject;
import com.yomahub.liteflow.slot.DefaultContext;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;

/**
 * FilterJudgeCmp 测试类
 * 测试 evaluateConditions 方法的各种表达式评估功能
 */
public class FilterJudgeCmpTest {

    private DefaultContext context;
    private JSONObject testData;

    @BeforeEach
    public void setUp() {
        context = new DefaultContext();
        
        // 设置测试数据
        testData = new JSONObject();
        testData.put("name", "张三");
        testData.put("age", 25);
        testData.put("score", 85.5);
        testData.put("status", "active");
        testData.put("isVip", true);
        
        // 嵌套对象
        JSONObject address = new JSONObject();
        address.put("city", "北京");
        address.put("district", "朝阳区");
        testData.put("address", address);
        
        // 设置全局上下文数据
        JSONObject globalData = new JSONObject();
        globalData.put("minAge", 18);
        globalData.put("maxScore", 100);
        context.setData("global", globalData);
    }

    @Test
    public void testSimpleComparison() {
        // 测试简单比较
        assertTrue(FilterJudgeCmp.evaluateConditions("age > 20", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("age >= 25", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("age < 30", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("age <= 25", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("age == 25", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("age != 30", testData, context));
        
        assertFalse(FilterJudgeCmp.evaluateConditions("age > 30", testData, context));
        assertFalse(FilterJudgeCmp.evaluateConditions("age < 20", testData, context));
    }

    @Test
    public void testStringComparison() {
        // 测试字符串比较
        assertTrue(FilterJudgeCmp.evaluateConditions("name == '张三'", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("status == 'active'", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("name != '李四'", testData, context));
        
        // 测试字符串操作
        assertTrue(FilterJudgeCmp.evaluateConditions("name contains '张'", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("name startsWith '张'", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("name endsWith '三'", testData, context));
        
        assertFalse(FilterJudgeCmp.evaluateConditions("name contains '李'", testData, context));
    }

    @Test
    public void testBooleanComparison() {
        // 测试布尔值比较
        assertTrue(FilterJudgeCmp.evaluateConditions("isVip == true", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("isVip", testData, context));
        
        assertFalse(FilterJudgeCmp.evaluateConditions("isVip == false", testData, context));
    }

    @Test
    public void testNestedProperty() {
        // 测试嵌套属性访问
        assertTrue(FilterJudgeCmp.evaluateConditions("address.city == '北京'", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("address.district == '朝阳区'", testData, context));
        
        assertFalse(FilterJudgeCmp.evaluateConditions("address.city == '上海'", testData, context));
    }

    @Test
    public void testLogicalOperators() {
        // 测试逻辑操作符
        assertTrue(FilterJudgeCmp.evaluateConditions("age > 20 && status == 'active'", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("age > 30 || status == 'active'", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("!(age < 20)", testData, context));
        
        assertFalse(FilterJudgeCmp.evaluateConditions("age > 30 && status == 'active'", testData, context));
        assertFalse(FilterJudgeCmp.evaluateConditions("age > 30 || status == 'inactive'", testData, context));
    }

    @Test
    public void testComplexExpression() {
        // 测试复杂表达式
        assertTrue(FilterJudgeCmp.evaluateConditions(
            "(age >= 18 && age <= 60) && (status == 'active' || isVip == true)", 
            testData, context));
        
        assertTrue(FilterJudgeCmp.evaluateConditions(
            "name contains '张' && address.city == '北京' && score > 80", 
            testData, context));
    }

    @Test
    public void testContextVariables() {
        // 设置上下文变量
        context.setData("nodeA.data.threshold", 20);
        
        // 测试上下文变量替换（需要先在表达式中使用${...}语法）
        // 注意：这里需要根据实际的VariableReplacerUtils实现来调整测试
        assertTrue(FilterJudgeCmp.evaluateConditions("age > 18", testData, context));
    }

    @Test
    public void testInOperator() {
        // 测试in操作符
        assertTrue(FilterJudgeCmp.evaluateConditions("status in ['active', 'pending']", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("age in [25, 30, 35]", testData, context));
        
        assertFalse(FilterJudgeCmp.evaluateConditions("status in ['inactive', 'disabled']", testData, context));
        assertFalse(FilterJudgeCmp.evaluateConditions("status not in ['active', 'pending']", testData, context));
    }

    @Test
    public void testRegexMatches() {
        // 测试正则匹配
        assertTrue(FilterJudgeCmp.evaluateConditions("name matches '张.*'", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("status matches 'act.*'", testData, context));
        
        assertFalse(FilterJudgeCmp.evaluateConditions("name matches '李.*'", testData, context));
    }

    @Test
    public void testEdgeCases() {
        // 测试边界情况
        assertTrue(FilterJudgeCmp.evaluateConditions("", testData, context)); // 空表达式
        assertTrue(FilterJudgeCmp.evaluateConditions("   ", testData, context)); // 空白表达式
        
        // 测试null值
        testData.put("nullField", null);
        assertFalse(FilterJudgeCmp.evaluateConditions("nullField == 'test'", testData, context));
        assertTrue(FilterJudgeCmp.evaluateConditions("nullField == null", testData, context));
    }

    @Test
    public void testErrorHandling() {
        // 测试错误处理
        assertFalse(FilterJudgeCmp.evaluateConditions("nonExistentField > 10", testData, context));
        assertFalse(FilterJudgeCmp.evaluateConditions("age > invalidValue", testData, context));
        
        // 测试无效的正则表达式
        assertFalse(FilterJudgeCmp.evaluateConditions("name matches '[invalid'", testData, context));
    }
}
