package com.chis.zyjk.bigdata.alert.liteflow.utils;

import com.chis.project.frame.common.tools.core.util.StrUtil;
import com.chis.project.frame.common.tools.json.JSONArray;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.yomahub.liteflow.slot.DefaultContext;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 条件评估工具类
 * 提供统一的条件评估逻辑，支持简单和复杂模式
 *
 * <AUTHOR> Assistant
 * @since 2024-01-17
 */
@Slf4j
public final class ConditionEvaluatorUtils {

    /**
     * 评估条件配置（支持简单和复杂模式）
     *
     * @param config    节点配置
     * @param inputData 输入数据
     * @param context   LiteFlow上下文
     * @return 评估结果
     */
    public static boolean evaluateConditions(JSONObject config, Object inputData, DefaultContext context) {
        if (config.containsKey("conditionGroups")) {
            // 使用复杂模式：groupLogicOperator + conditionGroups
            return evaluateConditionGroups(config, inputData, context);
        } else {
            // 使用简单模式：logicOperator + conditions
            return evaluateSimpleConditions(config, inputData, context);
        }
    }

    /**
     * 评估简单模式条件
     *
     * @param config    节点配置
     * @param inputData 输入数据
     * @param context   LiteFlow上下文
     * @return 评估结果
     */
    public static boolean evaluateSimpleConditions(JSONObject config, Object inputData, DefaultContext context) {
        JSONArray conditions = config.getJSONArray("conditions");
        if (conditions == null || conditions.isEmpty()) {
            log.debug("条件配置为空，默认返回true");
            return true;
        }

        String logicOperator = config.getStr("logicOperator", "AND");
        boolean isAnd = "AND".equalsIgnoreCase(logicOperator);

        boolean finalResult = isAnd; // AND初始为true，OR初始为false

        for (int i = 0; i < conditions.size(); i++) {
            JSONObject condition = conditions.getJSONObject(i);
            if (condition == null) {
                continue;
            }

            boolean conditionResult = evaluateSingleCondition(condition, inputData, context);

            if (isAnd) {
                finalResult = finalResult && conditionResult;
                if (!finalResult) {
                    break; // AND模式下，有一个条件为false就可以提前退出
                }
            } else {
                finalResult = finalResult || conditionResult;
                if (finalResult) {
                    break; // OR模式下，有一个条件为true就可以提前退出
                }
            }
        }

        return finalResult;
    }

    /**
     * 评估复杂模式条件组
     *
     * @param config    节点配置
     * @param inputData 输入数据
     * @param context   LiteFlow上下文
     * @return 评估结果
     */
    public static boolean evaluateConditionGroups(JSONObject config, Object inputData, DefaultContext context) {
        JSONArray conditionGroups = config.getJSONArray("conditionGroups");
        if (conditionGroups == null || conditionGroups.isEmpty()) {
            log.debug("条件组配置为空，默认返回true");
            return true;
        }

        String groupLogicOperator = config.getStr("groupLogicOperator", "AND");
        boolean isAnd = "AND".equalsIgnoreCase(groupLogicOperator);

        boolean finalResult = isAnd; // AND初始为true，OR初始为false

        for (int i = 0; i < conditionGroups.size(); i++) {
            JSONObject group = conditionGroups.getJSONObject(i);
            if (group == null) {
                continue;
            }

            // 评估单个条件组
            boolean groupResult = evaluateSimpleConditions(group, inputData, context);

            if (isAnd) {
                finalResult = finalResult && groupResult;
                if (!finalResult) {
                    break; // AND模式下，有一个组为false就可以提前退出
                }
            } else {
                finalResult = finalResult || groupResult;
                if (finalResult) {
                    break; // OR模式下，有一个组为true就可以提前退出
                }
            }
        }

        return finalResult;
    }

    /**
     * 评估单个条件（支持变量替换）
     *
     * @param condition 条件配置
     * @param inputData 输入数据
     * @param context   LiteFlow上下文
     * @return 判断结果
     */
    public static boolean evaluateSingleCondition(JSONObject condition, Object inputData, DefaultContext context) {
        String field = condition.getStr("field");
        String operator = condition.getStr("operator");
        Object expectedValue = condition.get("value");
        String dataType = condition.getStr("dataType", "string");

        // 对字段名进行变量替换
        if (field != null && field.contains("${")) {
            field = VariableReplacerUtils.replaceVariables(field, context);
        }

        // 对期望值进行变量替换
        if (expectedValue instanceof String && ((String) expectedValue).contains("${")) {
            expectedValue = VariableReplacerUtils.replaceVariables((String) expectedValue, context);
        }

        // 获取字段值（支持动态字段表达式）
        Object fieldValue = getFieldValue(inputData, field, context);

        // 处理特殊值
        if (fieldValue == null) {
            return handleNullValue(operator, expectedValue);
        }

        if (StrUtil.isBlank(fieldValue.toString())) {
            return handleEmptyValue(operator, expectedValue);
        }

        // 执行比较
        return compareValues(fieldValue, operator, expectedValue, dataType, condition);
    }

    /**
     * 获取字段值（支持动态字段表达式和静态字段名）
     *
     * @param data      数据对象（可以为null，当使用动态字段表达式时）
     * @param fieldPath 字段路径，支持点号分隔或动态表达式
     * @param context   LiteFlow上下文（用于解析动态表达式）
     * @return 字段值
     */
    public static Object getFieldValue(Object data, String fieldPath, DefaultContext context) {
        if (StrUtil.isBlank(fieldPath)) {
            return null;
        }

        // 如果是动态字段表达式（包含${}），直接使用变量替换
        if (fieldPath.contains("${")) {
            return VariableReplacerUtils.replaceVariables(fieldPath, context);
        }

        // 静态字段名，从data对象中获取
        if (data == null) {
            return null;
        }

        String[] parts = fieldPath.split("\\.");
        Object current = data;

        for (String part : parts) {
            if (current == null) {
                return null;
            }

            if (current instanceof Map) {
                current = ((Map<?, ?>) current).get(part);
            } else if (current instanceof JSONObject) {
                current = ((JSONObject) current).get(part);
            } else {
                try {
                    current = current.getClass().getField(part).get(current);
                } catch (Exception e) {
                    log.debug("无法获取字段值: {}.{}", current.getClass().getSimpleName(), part);
                    return null;
                }
            }
        }

        return current;
    }

    /**
     * 处理空值情况
     */
    private static boolean handleNullValue(String operator, Object expectedValue) {
        switch (operator.toLowerCase()) {
            case "isnull":
                return true;
            case "isnotnull":
                return false;
            case "isempty":
                return true;
            case "isnotempty":
                return false;
            default:
                return false;
        }
    }

    /**
     * 处理空字符串情况
     */
    private static boolean handleEmptyValue(String operator, Object expectedValue) {
        switch (operator.toLowerCase()) {
            case "isempty":
                return true;
            case "isnotempty":
                return false;
            case "isnull":
                return false;
            case "isnotnull":
                return true;
            default:
                return false;
        }
    }

    /**
     * 比较值
     *
     * @param fieldValue    字段值
     * @param operator      操作符
     * @param expectedValue 期望值
     * @param dataType      数据类型
     * @param condition     条件配置
     * @return 比较结果
     */
    public static boolean compareValues(Object fieldValue, String operator, Object expectedValue, String dataType, JSONObject condition) {
        if (fieldValue == null || operator == null) {
            return false;
        }

        try {
            switch (operator.toLowerCase()) {
                case "equals":
                case "eq":
                    return compareEquals(fieldValue, expectedValue, dataType, condition);
                case "notequals":
                case "ne":
                    return !compareEquals(fieldValue, expectedValue, dataType, condition);
                case "greaterthan":
                case "gt":
                    return compareGreaterThan(fieldValue, expectedValue, dataType);
                case "greaterthanorequal":
                case "gte":
                    return compareGreaterThanOrEqual(fieldValue, expectedValue, dataType);
                case "lessthan":
                case "lt":
                    return compareLessThan(fieldValue, expectedValue, dataType);
                case "lessthanorequal":
                case "lte":
                    return compareLessThanOrEqual(fieldValue, expectedValue, dataType);
                case "contains":
                    return compareContains(fieldValue, expectedValue, condition);
                case "notcontains":
                    return !compareContains(fieldValue, expectedValue, condition);
                case "startswith":
                    return compareStartsWith(fieldValue, expectedValue, condition);
                case "endswith":
                    return compareEndsWith(fieldValue, expectedValue, condition);
                case "in":
                    return compareIn(fieldValue, expectedValue, dataType, condition);
                case "notin":
                    return !compareIn(fieldValue, expectedValue, dataType, condition);
                case "isnull":
                    return fieldValue == null;
                case "isnotnull":
                    return fieldValue != null;
                case "isempty":
                    return StrUtil.isBlank(fieldValue.toString());
                case "isnotempty":
                    return StrUtil.isNotBlank(fieldValue.toString());
                default:
                    log.warn("不支持的操作符: {}", operator);
                    return false;
            }
        } catch (Exception e) {
            log.error("比较值时发生错误，operator: {}, fieldValue: {}, expectedValue: {}", operator, fieldValue, expectedValue, e);
            return false;
        }
    }

    /**
     * 等于比较
     */
    private static boolean compareEquals(Object fieldValue, Object expectedValue, String dataType, JSONObject condition) {
        if (fieldValue == null && expectedValue == null) {
            return true;
        }
        if (fieldValue == null || expectedValue == null) {
            return false;
        }

        switch (dataType.toLowerCase()) {
            case "number":
                return compareNumbers(fieldValue, expectedValue) == 0;
            case "date":
                return compareDates(fieldValue, expectedValue) == 0;
            case "boolean":
                return Boolean.valueOf(fieldValue.toString()).equals(Boolean.valueOf(expectedValue.toString()));
            default:
                boolean caseSensitive = condition.getBool("caseSensitive", true);
                String fieldStr = fieldValue.toString();
                String expectedStr = expectedValue.toString();
                return caseSensitive ? fieldStr.equals(expectedStr) : fieldStr.equalsIgnoreCase(expectedStr);
        }
    }

    /**
     * 大于比较
     */
    private static boolean compareGreaterThan(Object fieldValue, Object expectedValue, String dataType) {
        switch (dataType.toLowerCase()) {
            case "number":
                return compareNumbers(fieldValue, expectedValue) > 0;
            case "date":
                return compareDates(fieldValue, expectedValue) > 0;
            default:
                return fieldValue.toString().compareTo(expectedValue.toString()) > 0;
        }
    }

    /**
     * 大于等于比较
     */
    private static boolean compareGreaterThanOrEqual(Object fieldValue, Object expectedValue, String dataType) {
        switch (dataType.toLowerCase()) {
            case "number":
                return compareNumbers(fieldValue, expectedValue) >= 0;
            case "date":
                return compareDates(fieldValue, expectedValue) >= 0;
            default:
                return fieldValue.toString().compareTo(expectedValue.toString()) >= 0;
        }
    }

    /**
     * 小于比较
     */
    private static boolean compareLessThan(Object fieldValue, Object expectedValue, String dataType) {
        switch (dataType.toLowerCase()) {
            case "number":
                return compareNumbers(fieldValue, expectedValue) < 0;
            case "date":
                return compareDates(fieldValue, expectedValue) < 0;
            default:
                return fieldValue.toString().compareTo(expectedValue.toString()) < 0;
        }
    }

    /**
     * 小于等于比较
     */
    private static boolean compareLessThanOrEqual(Object fieldValue, Object expectedValue, String dataType) {
        switch (dataType.toLowerCase()) {
            case "number":
                return compareNumbers(fieldValue, expectedValue) <= 0;
            case "date":
                return compareDates(fieldValue, expectedValue) <= 0;
            default:
                return fieldValue.toString().compareTo(expectedValue.toString()) <= 0;
        }
    }

    /**
     * 包含比较
     */
    private static boolean compareContains(Object fieldValue, Object expectedValue, JSONObject condition) {
        boolean caseSensitive = condition.getBool("caseSensitive", true);
        String fieldStr = fieldValue.toString();
        String expectedStr = expectedValue.toString();

        if (caseSensitive) {
            return fieldStr.contains(expectedStr);
        } else {
            return fieldStr.toLowerCase().contains(expectedStr.toLowerCase());
        }
    }

    /**
     * 以...开始比较
     */
    private static boolean compareStartsWith(Object fieldValue, Object expectedValue, JSONObject condition) {
        boolean caseSensitive = condition.getBool("caseSensitive", true);
        String fieldStr = fieldValue.toString();
        String expectedStr = expectedValue.toString();

        if (caseSensitive) {
            return fieldStr.startsWith(expectedStr);
        } else {
            return fieldStr.toLowerCase().startsWith(expectedStr.toLowerCase());
        }
    }

    /**
     * 以...结束比较
     */
    private static boolean compareEndsWith(Object fieldValue, Object expectedValue, JSONObject condition) {
        boolean caseSensitive = condition.getBool("caseSensitive", true);
        String fieldStr = fieldValue.toString();
        String expectedStr = expectedValue.toString();

        if (caseSensitive) {
            return fieldStr.endsWith(expectedStr);
        } else {
            return fieldStr.toLowerCase().endsWith(expectedStr.toLowerCase());
        }
    }

    /**
     * 在集合中比较
     */
    private static boolean compareIn(Object fieldValue, Object expectedValue, String dataType, JSONObject condition) {
        if (!(expectedValue instanceof List)) {
            return false;
        }

        List<?> expectedList = (List<?>) expectedValue;
        for (Object item : expectedList) {
            if (compareEquals(fieldValue, item, dataType, condition)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 数字比较
     */
    private static int compareNumbers(Object value1, Object value2) {
        BigDecimal num1 = new BigDecimal(value1.toString());
        BigDecimal num2 = new BigDecimal(value2.toString());
        return num1.compareTo(num2);
    }

    /**
     * 日期比较
     */
    private static int compareDates(Object value1, Object value2) {
        Date date1 = parseDate(value1);
        Date date2 = parseDate(value2);

        if (date1 == null || date2 == null) {
            throw new IllegalArgumentException("无法解析日期: " + value1 + ", " + value2);
        }

        return date1.compareTo(date2);
    }

    /**
     * 解析日期
     */
    private static Date parseDate(Object value) {
        if (value instanceof Date) {
            return (Date) value;
        }
        if (value instanceof Long) {
            return new Date((Long) value);
        }
        // 这里可以添加更多日期格式解析逻辑
        return null;
    }
}
