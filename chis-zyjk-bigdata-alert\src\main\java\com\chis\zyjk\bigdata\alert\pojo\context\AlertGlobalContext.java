package com.chis.zyjk.bigdata.alert.pojo.context;

import com.chis.zyjk.bigdata.alert.liteflow.pojo.context.GlobalContext;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 预警全局上下文
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AlertGlobalContext extends GlobalContext {
    /**
     * 重复去重表达式
     */
    private String deDupKeyExpression;
    /**
     * 预警值表达式
     */
    private String alertValueExpression;
}
