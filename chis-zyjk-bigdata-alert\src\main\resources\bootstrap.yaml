server:
  port: 20012
  servlet:
    context-path: /bigdata-alert

spring:
  application:
    # 应用名称
    name: chis-zyjk-bigdata-alert
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      # nacos server开启鉴权后需要配置
      # username: nacos
      # password: nacos
      # 服务注册配置
      discovery:
        server-addr: 10.88.99.11:8848
        namespace: cc-test
      # 配置中心配置
      config:
        server-addr: 10.88.99.11:8848
        namespace: cc-test
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}

# 为日志输出路径，全路径为nacos中的全路径配置logging.file: ${logging.dir}${spring.application.name}/chiscdc.log
logging:
  dir: logs/
  level:
    com.chis.zyjk.api.liteflow.alarm: DEBUG
    com.yomahub.liteflow: INFO

# LiteFlow配置
liteflow:
  # 是否启用监控
  monitor:
    enable: true
  # 线程池配置
  thread-executor:
    max-workers: 16
  # 重试配置
  retry:
    max-count: 3
