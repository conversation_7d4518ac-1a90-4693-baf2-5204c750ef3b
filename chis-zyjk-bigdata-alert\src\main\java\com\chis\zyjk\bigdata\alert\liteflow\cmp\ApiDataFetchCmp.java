package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.bigdata.alert.liteflow.common.ContextHelper;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowCmpException;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowErrorCode;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowExceptionHelper;
import com.chis.zyjk.bigdata.alert.liteflow.pojo.context.CmpContext;
import com.chis.zyjk.bigdata.alert.liteflow.utils.BaseApiNodeUtils;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.log.LFLog;
import com.yomahub.liteflow.log.LFLoggerManager;

import java.util.List;
import java.util.Map;

/**
 * API数据获取组件
 * <p></p>
 * <b>apiDataFetch</b>
 */
@LiteflowComponent(id = "apiDataFetch", name = "API数据获取组件")
public class ApiDataFetchCmp extends NodeComponent {
    private final LFLog logger = LFLoggerManager.getLogger(FlowExecutor.class);

    @Override
    public void process() {
        String nodeId = this.getNodeId();
        String name = this.getName();
        String tag = this.getTag();
        if (ObjectUtil.isEmpty(tag)) {
            throw LiteFlowExceptionHelper.createNodeException(LiteFlowErrorCode.CMP_TAG_MISSING, this);
        }

        // logger.info("开始执行API数据单次获取节点，nodeId: {}, tag: {}", nodeId, tag);

        try {
            CmpContext cmpContext = this.getContextBean(CmpContext.class);
            // 上下文中获取组件配置
            JSONObject config = ContextHelper.getOrCreateContextConfig(cmpContext, tag);
            checkConfig(config);

            // 执行单次API数据获取
            List<Object> dataList = fetchSingleData(config, cmpContext);

            // 更新数据到上下文供其他节点使用
            BaseApiNodeUtils.updateCurrentDataToNodeHelper(tag, dataList, cmpContext);

            // logger.info("API数据单次获取节点执行完成，nodeId: {}, tag: {}, dataSize: {}", nodeId, tag, data.size());

        } catch (LiteFlowCmpException e) {
            throw e;
        } catch (Exception e) {
            throw LiteFlowExceptionHelper.createNodeException(
                    e,
                    LiteFlowErrorCode.CMP_EXECUTION_FAILED,
                    name + "执行失败: " + e.getMessage(),
                    this
            );
        }
    }

    /**
     * 验证组件必需配置
     *
     * @param config 组件配置
     */
    public void checkConfig(JSONObject config) {
        if (ObjectUtil.isNotEmpty(config)
                && config.containsKey("apiUrl")
                && ObjectUtil.isNotEmpty(config.get("apiUrl"))) {
            return;
        }
        throw LiteFlowExceptionHelper.createNodeException(
                LiteFlowErrorCode.CMP_CONFIG_VALIDATION_FAILED,
                LiteFlowErrorCode.CMP_CONFIG_VALIDATION_FAILED.getDesc() + "：缺少apiUrl配置",
                this
        );
    }

    /**
     * 执行单次API数据获取
     */
    private List<Object> fetchSingleData(JSONObject config, CmpContext context) {
        // 获取上下文进行变量替换
        // 构建API请求参数（支持变量替换）
        Map<String, Object> params = BaseApiNodeUtils.buildApiParams(config, context);

        // 构建请求头（支持变量替换）
        Map<String, String> headers = BaseApiNodeUtils.buildApiHeaders(config, context);

        String apiUrl = config.getStr("apiUrl");
        String method = config.getStr("method", "POST");
        int timeout = config.getInt("timeout", 30000);

        logger.debug("调用API获取数据, url: {}", apiUrl);

        // 执行API调用
        String responseBody = BaseApiNodeUtils.executeApiCall(apiUrl, method, params, headers, timeout, this.getTag());

        // 保存原始响应数据
        BaseApiNodeUtils.saveResponseToContext(context, this.getTag(), responseBody);

        // 提取结果数据
        return BaseApiNodeUtils.extractResultData(responseBody, config);
    }

}
