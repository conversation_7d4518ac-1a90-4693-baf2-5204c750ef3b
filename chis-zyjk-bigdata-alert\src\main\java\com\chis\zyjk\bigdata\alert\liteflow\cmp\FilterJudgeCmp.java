package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.project.frame.common.tools.core.collection.CollUtil;
import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.chis.project.frame.common.tools.core.util.StrUtil;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.bigdata.alert.liteflow.common.ContextHelper;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowCmpException;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowErrorCode;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowExceptionHelper;
import com.chis.zyjk.bigdata.alert.liteflow.pojo.context.CmpContext;
import com.chis.zyjk.bigdata.alert.liteflow.pojo.dto.FilterResult;
import com.chis.zyjk.bigdata.alert.liteflow.utils.BaseApiNodeUtils;
import com.chis.zyjk.bigdata.alert.liteflow.utils.VariableReplacerUtils;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.log.LFLog;
import com.yomahub.liteflow.log.LFLoggerManager;
import com.yomahub.liteflow.slot.DefaultContext;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class FilterJudgeCmp extends NodeComponent {
    private final LFLog logger = LFLoggerManager.getLogger(FlowExecutor.class);

    @Override
    public void process() {
        String nodeId = this.getNodeId();
        String name = this.getName();
        String tag = this.getTag();
        if (ObjectUtil.isEmpty(tag)) {
            throw LiteFlowExceptionHelper.createNodeException(LiteFlowErrorCode.CMP_TAG_MISSING, this);
        }

        try {
            CmpContext cmpContext = this.getContextBean(CmpContext.class);
            // 上下文中获取组件配置
            JSONObject config = ContextHelper.getOrCreateContextConfig(cmpContext, tag);
            checkConfig(config);

            // 获取输入数据字段名
            String inputDataPath = config.getStr("inputDataPath");
            Object inputData = ContextHelper.getContextData(inputDataPath, cmpContext);
            if (inputData == null) {
                logger.warn("输入数据为空，nodeId: {}, tag: {}, inputDataPath: {}", nodeId, tag, inputDataPath);
                outputFilterResult(new FilterResult(), cmpContext);
                return;
            }

            // 转换为列表
            List<Object> inputList = convertToList(inputData);
            if (ObjectUtil.isEmpty(inputList)) {
                logger.warn("输入数据列表为空，nodeId: {}, tag: {}", nodeId, tag);
                outputFilterResult(new FilterResult(), cmpContext);
                return;
            }

            // 执行过滤处理（使用工具类）
            FilterResult result = filterData(config, inputList, cmpContext);

            // 固定字段输出
            outputFilterResult(result, cmpContext);

        } catch (LiteFlowCmpException e) {
            throw e;
        } catch (Exception e) {
            throw LiteFlowExceptionHelper.createNodeException(
                    e,
                    LiteFlowErrorCode.CMP_EXECUTION_FAILED,
                    name + "执行失败: " + e.getMessage(),
                    this
            );
        }
    }

    /**
     * 验证组件必需配置
     *
     * @param config 组件配置
     */
    public void checkConfig(JSONObject config) {
        List<String> paramList = new ArrayList<>();
        if (ObjectUtil.isEmpty(config)) {
            paramList.add("inputDataPath");
            paramList.add("condition");
        } else {
            if (!config.containsKey("inputDataPath") && ObjectUtil.isEmpty(config.get("inputDataPath"))) {
                paramList.add("inputDataPath");
            }
            if (!config.containsKey("condition") && ObjectUtil.isEmpty(config.get("condition"))) {
                paramList.add("condition");
            }
        }

        if (paramList.isEmpty()) {
            return;
        }

        throw LiteFlowExceptionHelper.createNodeException(
                LiteFlowErrorCode.CMP_CONFIG_VALIDATION_FAILED,
                LiteFlowErrorCode.CMP_CONFIG_VALIDATION_FAILED.getDesc() + "：缺少" + CollUtil.join(paramList, "、") + "配置",
                this
        );
    }

    /**
     * 输出过滤结果
     *
     * @param result  过滤结果
     * @param context LiteFlow上下文
     */
    public void outputFilterResult(FilterResult result, DefaultContext context) {
        ContextHelper.setContextData(this.getTag(), "matchedData", result.getMatchedData(), context);
        ContextHelper.setContextData(this.getTag(), "unmatchedData", result.getUnmatchedData(), context);
    }

    /**
     * 转换为列表
     *
     * @param data 输入数据
     * @return 列表
     */
    @SuppressWarnings("unchecked")
    private List<Object> convertToList(Object data) {
        if (data == null) {
            return new ArrayList<>();
        }

        if (data instanceof List) {
            return (List<Object>) data;
        }

        List<Object> result = new ArrayList<>();
        result.add(data);
        return result;
    }

    /**
     * 过滤数据（使用工具类）
     *
     * @param config    节点配置
     * @param inputList 输入数据列表
     * @param context   LiteFlow上下文
     * @return 过滤结果
     */
    private FilterResult filterData(JSONObject config, List<Object> inputList, DefaultContext context) {
        FilterResult result = new FilterResult();

        String condition = config.getStr("condition");

        for (Object record : inputList) {
            // 使用SpEL 表达式工具类评估条件
            boolean matches = evaluateConditions(condition, record, context);

            if (matches) {
                result.getMatchedData().add(record);
            } else {
                result.getUnmatchedData().add(record);
            }
        }
        return result;
    }

    /**
     * 评估条件配置（SpEL 表达式）
     *
     * @param condition 条件表达式(带点的参数从context中获取，不带点的参数就是inputData的属性)
     * @param inputData 输入数据
     * @param context   LiteFlow上下文
     * @return 评估结果
     */
    public static boolean evaluateConditions(String condition, Object inputData, DefaultContext context) {
        if (StrUtil.isBlank(condition)) {
            return true; // 空条件默认为true
        }

        try {
            // 解析并评估SpEL表达式
            return evaluateSpelExpression(condition, inputData, context);
        } catch (Exception e) {
            // 记录错误日志并返回false
            System.err.println("条件表达式评估失败: " + condition + ", 错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 评估SpEL表达式
     * 支持的语法：
     * - 简单属性访问：name, age, status
     * - 嵌套属性访问：user.name, order.customer.id
     * - 上下文变量访问：${global.config}, ${nodeId.data.field}
     * - 比较操作：==, !=, >, <, >=, <=
     * - 逻辑操作：&&, ||, !
     * - 字符串操作：contains, startsWith, endsWith
     * - 数值操作：+, -, *, /, %
     * - 集合操作：in, not in
     * - 正则匹配：matches
     *
     * @param expression SpEL表达式
     * @param inputData  输入数据对象
     * @param context    LiteFlow上下文
     * @return 评估结果
     */
    private static boolean evaluateSpelExpression(String expression, Object inputData, DefaultContext context) {
        // 预处理表达式：替换上下文变量
        String processedExpression = preprocessExpression(expression, context);

        // 解析并评估表达式
        return parseAndEvaluateExpression(processedExpression, inputData, context);
    }

    /**
     * 预处理表达式：替换其中的上下文变量
     *
     * @param expression 原始表达式
     * @param context    LiteFlow上下文
     * @return 处理后的表达式
     */
    private static String preprocessExpression(String expression, DefaultContext context) {
        // 使用VariableReplacerUtils替换${...}变量
        return VariableReplacerUtils.replaceVariables(expression, context);
    }

    /**
     * 解析并评估表达式
     * 支持基本的逻辑和比较操作
     *
     * @param expression 预处理后的表达式
     * @param inputData  输入数据
     * @param context    上下文
     * @return 评估结果
     */
    private static boolean parseAndEvaluateExpression(String expression, Object inputData, DefaultContext context) {
        expression = expression.trim();

        // 处理逻辑操作符（优先级：! > && > ||）
        if (expression.contains("||")) {
            return evaluateOrExpression(expression, inputData, context);
        }

        if (expression.contains("&&")) {
            return evaluateAndExpression(expression, inputData, context);
        }

        if (expression.startsWith("!")) {
            return !parseAndEvaluateExpression(expression.substring(1).trim(), inputData, context);
        }

        // 处理括号
        if (expression.startsWith("(") && expression.endsWith(")")) {
            return parseAndEvaluateExpression(expression.substring(1, expression.length() - 1), inputData, context);
        }

        // 处理比较操作
        return evaluateComparisonExpression(expression, inputData, context);
    }

    /**
     * 评估OR表达式
     */
    private static boolean evaluateOrExpression(String expression, Object inputData, DefaultContext context) {
        String[] parts = splitByOperator(expression, "\\|\\|");
        for (String part : parts) {
            if (parseAndEvaluateExpression(part.trim(), inputData, context)) {
                return true; // OR操作，有一个为true就返回true
            }
        }
        return false;
    }

    /**
     * 评估AND表达式
     */
    private static boolean evaluateAndExpression(String expression, Object inputData, DefaultContext context) {
        String[] parts = splitByOperator(expression, "&&");
        for (String part : parts) {
            if (!parseAndEvaluateExpression(part.trim(), inputData, context)) {
                return false; // AND操作，有一个为false就返回false
            }
        }
        return true;
    }

    /**
     * 分割操作符，考虑括号嵌套
     */
    private static String[] splitByOperator(String expression, String operator) {
        List<String> parts = new ArrayList<>();
        int start = 0;
        int parentheses = 0;

        Pattern pattern = Pattern.compile(operator);
        Matcher matcher = pattern.matcher(expression);

        while (matcher.find()) {
            // 检查当前位置是否在括号内
            String beforeMatch = expression.substring(start, matcher.start());
            for (char c : beforeMatch.toCharArray()) {
                if (c == '(') parentheses++;
                else if (c == ')') parentheses--;
            }

            if (parentheses == 0) {
                parts.add(expression.substring(start, matcher.start()));
                start = matcher.end();
            }
        }

        parts.add(expression.substring(start));
        return parts.toArray(new String[0]);
    }

    /**
     * 评估比较表达式
     */
    private static boolean evaluateComparisonExpression(String expression, Object inputData, DefaultContext context) {
        // 支持的比较操作符（按长度排序，避免匹配错误）
        String[] operators = {">=", "<=", "!=", "==", ">", "<", "contains", "startsWith", "endsWith", "matches", "in", "not in"};

        for (String operator : operators) {
            if (expression.contains(operator)) {
                String[] parts = expression.split(Pattern.quote(operator), 2);
                if (parts.length == 2) {
                    String leftExpr = parts[0].trim();
                    String rightExpr = parts[1].trim();

                    Object leftValue = getExpressionValue(leftExpr, inputData, context);
                    Object rightValue = getExpressionValue(rightExpr, inputData, context);

                    return compareValues(leftValue, operator, rightValue);
                }
            }
        }

        // 如果没有比较操作符，尝试作为布尔值或存在性检查
        Object value = getExpressionValue(expression, inputData, context);
        return toBooleanValue(value);
    }

    /**
     * 获取表达式的值
     */
    private static Object getExpressionValue(String expression, Object inputData, DefaultContext context) {
        expression = expression.trim();

        // 移除引号（字符串字面量）
        if ((expression.startsWith("'") && expression.endsWith("'")) ||
            (expression.startsWith("\"") && expression.endsWith("\""))) {
            return expression.substring(1, expression.length() - 1);
        }

        // 数值字面量
        if (expression.matches("-?\\d+")) {
            return Long.parseLong(expression);
        }
        if (expression.matches("-?\\d+\\.\\d+")) {
            return Double.parseDouble(expression);
        }

        // 布尔字面量
        if ("true".equalsIgnoreCase(expression)) {
            return true;
        }
        if ("false".equalsIgnoreCase(expression)) {
            return false;
        }

        // null字面量
        if ("null".equalsIgnoreCase(expression)) {
            return null;
        }

        // 属性访问
        return getFieldValue(inputData, expression, context);
    }

    /**
     * 获取字段值（支持嵌套属性访问）
     */
    private static Object getFieldValue(Object data, String fieldPath, DefaultContext context) {
        if (StrUtil.isBlank(fieldPath) || data == null) {
            return null;
        }

        String[] parts = fieldPath.split("\\.");
        Object current = data;

        for (String part : parts) {
            if (current == null) {
                return null;
            }

            if (current instanceof JSONObject) {
                current = ((JSONObject) current).get(part);
            } else if (current instanceof Map) {
                current = ((Map<?, ?>) current).get(part);
            } else {
                // 尝试通过反射获取属性值
                current = getPropertyValue(current, part);
            }
        }

        return current;
    }

    /**
     * 通过反射获取属性值
     */
    private static Object getPropertyValue(Object obj, String propertyName) {
        if (obj == null || StrUtil.isBlank(propertyName)) {
            return null;
        }

        try {
            // 尝试getter方法
            String getterName = "get" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);
            java.lang.reflect.Method getter = obj.getClass().getMethod(getterName);
            return getter.invoke(obj);
        } catch (Exception e) {
            try {
                // 尝试is方法（布尔类型）
                String isMethodName = "is" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);
                java.lang.reflect.Method isMethod = obj.getClass().getMethod(isMethodName);
                return isMethod.invoke(obj);
            } catch (Exception e2) {
                try {
                    // 尝试直接访问字段
                    java.lang.reflect.Field field = obj.getClass().getDeclaredField(propertyName);
                    field.setAccessible(true);
                    return field.get(obj);
                } catch (Exception e3) {
                    return null;
                }
            }
        }
    }

    /**
     * 比较两个值
     */
    private static boolean compareValues(Object leftValue, String operator, Object rightValue) {
        switch (operator) {
            case "==":
                return objectEquals(leftValue, rightValue);
            case "!=":
                return !objectEquals(leftValue, rightValue);
            case ">":
                return compareNumbers(leftValue, rightValue) > 0;
            case "<":
                return compareNumbers(leftValue, rightValue) < 0;
            case ">=":
                return compareNumbers(leftValue, rightValue) >= 0;
            case "<=":
                return compareNumbers(leftValue, rightValue) <= 0;
            case "contains":
                return stringContains(leftValue, rightValue);
            case "startsWith":
                return stringStartsWith(leftValue, rightValue);
            case "endsWith":
                return stringEndsWith(leftValue, rightValue);
            case "matches":
                return stringMatches(leftValue, rightValue);
            case "in":
                return valueInCollection(leftValue, rightValue);
            case "not in":
                return !valueInCollection(leftValue, rightValue);
            default:
                return false;
        }
    }

    /**
     * 对象相等比较
     */
    private static boolean objectEquals(Object left, Object right) {
        if (left == null && right == null) {
            return true;
        }
        if (left == null || right == null) {
            return false;
        }

        // 字符串比较
        if (left instanceof String || right instanceof String) {
            return String.valueOf(left).equals(String.valueOf(right));
        }

        // 数值比较
        if (left instanceof Number && right instanceof Number) {
            return compareNumbers(left, right) == 0;
        }

        return left.equals(right);
    }

    /**
     * 数值比较
     */
    private static int compareNumbers(Object left, Object right) {
        if (!(left instanceof Number) || !(right instanceof Number)) {
            throw new IllegalArgumentException("无法比较非数值类型: " + left + " 和 " + right);
        }

        double leftDouble = ((Number) left).doubleValue();
        double rightDouble = ((Number) right).doubleValue();

        return Double.compare(leftDouble, rightDouble);
    }

    /**
     * 字符串包含检查
     */
    private static boolean stringContains(Object left, Object right) {
        if (left == null || right == null) {
            return false;
        }
        return String.valueOf(left).contains(String.valueOf(right));
    }

    /**
     * 字符串开始检查
     */
    private static boolean stringStartsWith(Object left, Object right) {
        if (left == null || right == null) {
            return false;
        }
        return String.valueOf(left).startsWith(String.valueOf(right));
    }

    /**
     * 字符串结束检查
     */
    private static boolean stringEndsWith(Object left, Object right) {
        if (left == null || right == null) {
            return false;
        }
        return String.valueOf(left).endsWith(String.valueOf(right));
    }

    /**
     * 正则匹配检查
     */
    private static boolean stringMatches(Object left, Object right) {
        if (left == null || right == null) {
            return false;
        }
        try {
            return String.valueOf(left).matches(String.valueOf(right));
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 值在集合中检查
     */
    private static boolean valueInCollection(Object value, Object collection) {
        if (collection == null) {
            return false;
        }

        if (collection instanceof List) {
            return ((List<?>) collection).contains(value);
        }

        if (collection instanceof Object[]) {
            Object[] array = (Object[]) collection;
            for (Object item : array) {
                if (objectEquals(value, item)) {
                    return true;
                }
            }
        }

        // 尝试将字符串解析为数组
        if (collection instanceof String) {
            String collectionStr = (String) collection;
            if (collectionStr.startsWith("[") && collectionStr.endsWith("]")) {
                String content = collectionStr.substring(1, collectionStr.length() - 1);
                String[] items = content.split(",");
                for (String item : items) {
                    if (objectEquals(value, item.trim())) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 转换为布尔值
     */
    private static boolean toBooleanValue(Object value) {
        if (value == null) {
            return false;
        }

        if (value instanceof Boolean) {
            return (Boolean) value;
        }

        if (value instanceof Number) {
            return ((Number) value).doubleValue() != 0;
        }

        if (value instanceof String) {
            String str = (String) value;
            return !str.isEmpty() && !"false".equalsIgnoreCase(str) && !"0".equals(str);
        }

        return true; // 非null对象默认为true
    }


}
