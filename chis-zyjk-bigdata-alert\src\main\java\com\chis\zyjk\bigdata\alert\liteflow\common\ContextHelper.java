package com.chis.zyjk.bigdata.alert.liteflow.common;

import com.chis.project.frame.common.tools.core.util.StrUtil;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.bigdata.alert.liteflow.utils.VariableReplacerUtils;
import com.yomahub.liteflow.slot.DefaultContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * LiteFlow上下文辅助工具类
 * <p>
 * 提供数据传递、上下文管理、变量替换等功能
 */
@Slf4j
@Component
public class ContextHelper {

    public static final String DATA_CONTEXT_KEY = ".data";
    public static final String CONFIG_CONTEXT_KEY = ".config";

    /**
     * 获取或创建上下文
     */
    public static JSONObject getOrCreateContext(DefaultContext context, String path) {
        Object dataObject = ContextHelper.getContextData(path, context);
        JSONObject contextData;
        if (dataObject instanceof JSONObject) {
            contextData = (JSONObject) dataObject;
        } else {
            contextData = new JSONObject();
            ContextHelper.setContextData(path, contextData, context);
        }
        return contextData;
    }

    /**
     * 获取或创建配置上下文
     */
    public static JSONObject getOrCreateContextConfig(DefaultContext context, String tag) {
        String path = tag + CONFIG_CONTEXT_KEY;
        return getOrCreateContext(context, path);
    }

    /**
     * 获取或创建数据上下文
     */
    public static JSONObject getOrCreateContextData(DefaultContext context, String tag) {
        String path = tag + DATA_CONTEXT_KEY;
        return getOrCreateContext(context, path);
    }

    /**
     * 设置上下文数据
     *
     * @param contextPath 上下文路径
     * @param value       值
     * @param context     LiteFlow执行上下文
     */
    public static void setContextData(String contextPath, Object value, DefaultContext context) {
        try {
            setNestedValueToContext(contextPath, value, context);
        } catch (Exception e) {
            log.error("设置上下文数据失败，path: {}", contextPath, e);
            throw e;
        }
    }

    /**
     * 根据tag设置数据上下文数据
     *
     * @param tag         tag
     * @param contextPath 上下文路径
     * @param value       值
     * @param context     LiteFlow执行上下文
     */
    public static void setContextData(String tag, String contextPath, Object value, DefaultContext context) {
        try {
            setNestedValueToContext(tag + DATA_CONTEXT_KEY + contextPath, value, context);
        } catch (Exception e) {
            log.error("设置上下文数据失败，path: {}", contextPath, e);
            throw e;
        }
    }

    /**
     * 获取上下文数据
     *
     * @param contextPath 上下文路径
     * @param context     LiteFlow执行上下文
     * @return 数据值
     */
    public static Object getContextData(String contextPath, DefaultContext context) {
        try {
            return getNestedValueFromContext(contextPath, context);
        } catch (Exception e) {
            log.error("获取上下文数据失败，path: {}", contextPath, e);
            throw e;
        }
    }

    /**
     * 根据tag获取数据上下文数据
     *
     * @param tag         tag
     * @param contextPath 上下文路径
     * @param context     LiteFlow执行上下文
     * @return 数据值
     */
    public static Object getContextData(String tag, String contextPath, DefaultContext context) {
        try {
            return getNestedValueFromContext(tag + DATA_CONTEXT_KEY + contextPath, context);
        } catch (Exception e) {
            log.error("获取上下文数据失败，path: {}", contextPath, e);
            throw e;
        }
    }

    /**
     * 变量替换
     * 支持 ${global.field}、${nodeId.currentItem.field}、${nodeId.currentData} 等语法
     *
     * @param template 模板字符串
     * @param context  LiteFlow上下文
     * @return 替换后的字符串
     */
    public static String replaceVariables(String template, DefaultContext context) {
        if (StrUtil.isBlank(template)) {
            return template;
        }

        try {
            return VariableReplacerUtils.replaceVariables(template, context);
        } catch (Exception e) {
            log.error("变量替换失败，template: {}", template, e);
            return template;
        }
    }

    /**
     * 从Context获取嵌套值
     *
     * @param path    路径，支持点号分隔
     * @param context LiteFlow执行上下文
     * @return 值
     */
    private static Object getNestedValueFromContext(String path, DefaultContext context) {
        if (StrUtil.isBlank(path)) {
            return null;
        }

        String[] parts = path.split("\\.");

        // 从Context获取根对象
        Object current = context.getData(parts[0]);

        // 遍历路径获取嵌套值
        for (int i = 1; i < parts.length; i++) {
            if (current == null) {
                return null;
            }

            if (current instanceof JSONObject) {
                current = ((JSONObject) current).get(parts[i]);
            } else if (current instanceof Map) {
                current = ((Map<?, ?>) current).get(parts[i]);
            } else {
                return null;
            }
        }

        return current;
    }

    /**
     * 设置嵌套值到Context
     *
     * @param path    路径，支持点号分隔
     * @param value   值
     * @param context LiteFlow执行上下文
     */
    @SuppressWarnings("unchecked")
    private static void setNestedValueToContext(String path, Object value, DefaultContext context) {
        if (StrUtil.isBlank(path)) {
            return;
        }

        String[] parts = path.split("\\.");
        if (parts.length == 1) {
            context.setData(parts[0], value);
            return;
        }

        // 获取或创建根对象
        Object data = context.getData(parts[0]);
        if (!(data instanceof Map)) {
            data = new JSONObject();
            context.setData(parts[0], data);
        }
        Map<String, Object> current = (Map<String, Object>) data;

        // 遍历路径创建嵌套结构
        for (int i = 0; i < parts.length - 1; i++) {
            String part = parts[i];
            Object next = current.get(part);

            if (!(next instanceof Map)) {
                next = new JSONObject();
                current.put(part, next);
            }

            current = (Map<String, Object>) next;
        }

        // 设置最终值
        current.put(parts[parts.length - 1], value);
    }


}
