package com.chis.zyjk.bigdata.alert.liteflow.exception;

import com.yomahub.liteflow.exception.LiteFlowException;
import lombok.Getter;

/**
 * LiteFlow组件级别异常
 */
@Getter
public class LiteFlowCmpException extends LiteFlowException {

    /**
     * 组件ID
     */
    private String nodeId;

    /**
     * 组件TAG
     */
    private String nodeTag;

    /**
     * 组件类型
     */
    private String nodeType;

    /**
     * 构造函数 - 使用错误码枚举
     *
     * @param errorCode 错误码枚举
     */
    public LiteFlowCmpException(LiteFlowErrorCode errorCode) {
        super(errorCode.getCode(), errorCode.getDesc());
    }

    /**
     * 构造函数 - 包含错误码枚举和组件信息
     *
     * @param errorCode 错误码枚举
     * @param nodeId    组件ID
     * @param nodeTag   组件TAG
     * @param nodeType  组件类型
     */
    public LiteFlowCmpException(LiteFlowErrorCode errorCode, String nodeId, String nodeTag, String nodeType) {
        this(errorCode);
        setAllNodeInfo(nodeId, nodeTag, nodeType);
    }

    /**
     * 构造函数 - 包含错误码枚举和原因异常
     *
     * @param errorCode 错误码枚举
     * @param cause     原因异常
     */
    public LiteFlowCmpException(LiteFlowErrorCode errorCode, Throwable cause) {
        super(errorCode.getCode(), errorCode.getDesc(), cause);
    }

    /**
     * 构造函数 - 包含错误码枚举和组件信息和原因异常
     *
     * @param errorCode 错误码枚举
     * @param nodeId    组件ID
     * @param nodeTag   组件TAG
     * @param nodeType  组件类型
     * @param cause     原因异常
     */
    public LiteFlowCmpException(LiteFlowErrorCode errorCode, String nodeId, String nodeTag, String nodeType, Throwable cause) {
        this(errorCode, cause);
        setAllNodeInfo(nodeId, nodeTag, nodeType);
    }

    /**
     * 构造函数 - 使用错误码枚举和自定义消息
     *
     * @param errorCode 错误码枚举
     * @param message   自定义消息
     */
    public LiteFlowCmpException(LiteFlowErrorCode errorCode, String message) {
        super(errorCode.getCode(), message);
    }

    /**
     * 构造函数 - 使用错误码枚举和自定义消息和组件信息
     *
     * @param errorCode 错误码枚举
     * @param message   自定义消息
     * @param nodeId    组件ID
     * @param nodeTag   组件TAG
     * @param nodeType  组件类型
     */
    public LiteFlowCmpException(LiteFlowErrorCode errorCode, String message, String nodeId, String nodeTag, String nodeType) {
        this(errorCode, message);
        setAllNodeInfo(nodeId, nodeTag, nodeType);
    }

    /**
     * 构造函数 - 使用错误码枚举和自定义消息和原因异常
     *
     * @param errorCode 错误码枚举
     * @param message   自定义消息
     * @param cause     原因异常
     */
    public LiteFlowCmpException(LiteFlowErrorCode errorCode, String message, Throwable cause) {
        super(errorCode.getCode(), message, cause);
    }

    /**
     * 构造函数 - 使用错误码枚举和自定义消息和组件信息和原因异常
     *
     * @param errorCode 错误码枚举
     * @param message   自定义消息
     * @param nodeId    组件ID
     * @param nodeTag   组件TAG
     * @param nodeType  组件类型
     * @param cause     原因异常
     */
    public LiteFlowCmpException(LiteFlowErrorCode errorCode, String message, String nodeId, String nodeTag, String nodeType, Throwable cause) {
        this(errorCode, message, cause);
        setAllNodeInfo(nodeId, nodeTag, nodeType);
    }

    /**
     * 设置组件信息
     *
     * @param nodeId   组件ID
     * @param nodeTag  组件TAG
     * @param nodeType 组件类型
     */
    public void setAllNodeInfo(String nodeId, String nodeTag, String nodeType) {
        this.nodeId = nodeId;
        this.nodeTag = nodeTag;
        this.nodeType = nodeType;
    }

    /**
     * 获取详细的异常信息
     *
     * @return 详细异常信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append("LiteFlow组件异常 [").append(super.getCode()).append("]: ").append(getMessage());

        if (nodeId != null) {
            sb.append(", 组件ID: ").append(nodeId);
        }

        if (nodeTag != null) {
            sb.append(", 组件TAG: ").append(nodeTag);
        }

        if (nodeType != null) {
            sb.append(", 组件类型: ").append(nodeType);
        }

        return sb.toString();
    }

    @Override
    public String toString() {
        return getDetailedMessage();
    }
}
