package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.project.frame.common.tools.core.collection.CollUtil;
import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.bigdata.alert.liteflow.common.ContextHelper;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowCmpException;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowErrorCode;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowExceptionHelper;
import com.chis.zyjk.bigdata.alert.liteflow.pojo.context.CmpContext;
import com.chis.zyjk.bigdata.alert.liteflow.pojo.dto.FilterResult;
import com.chis.zyjk.bigdata.alert.liteflow.utils.BaseApiNodeUtils;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.log.LFLog;
import com.yomahub.liteflow.log.LFLoggerManager;
import com.yomahub.liteflow.slot.DefaultContext;

import java.util.ArrayList;
import java.util.List;

public class FilterJudgeCmp extends NodeComponent {
    private final LFLog logger = LFLoggerManager.getLogger(FlowExecutor.class);

    @Override
    public void process() {
        String nodeId = this.getNodeId();
        String name = this.getName();
        String tag = this.getTag();
        if (ObjectUtil.isEmpty(tag)) {
            throw LiteFlowExceptionHelper.createNodeException(LiteFlowErrorCode.CMP_TAG_MISSING, this);
        }

        try {
            CmpContext cmpContext = this.getContextBean(CmpContext.class);
            // 上下文中获取组件配置
            JSONObject config = ContextHelper.getOrCreateContextConfig(cmpContext, tag);
            checkConfig(config);

            // 获取输入数据字段名
            String inputDataPath = config.getStr("inputDataPath");
            Object inputData = ContextHelper.getContextData(inputDataPath, cmpContext);
            if (inputData == null) {
                logger.warn("输入数据为空，nodeId: {}, tag: {}, inputDataPath: {}", nodeId, tag, inputDataPath);
                outputFilterResult(new FilterResult(), cmpContext);
                return;
            }

            // 转换为列表
            List<Object> inputList = convertToList(inputData);
            if (ObjectUtil.isEmpty(inputList)) {
                logger.warn("输入数据列表为空，nodeId: {}, tag: {}", nodeId, tag);
                outputFilterResult(new FilterResult(), cmpContext);
                return;
            }

            // 执行过滤处理（使用工具类）
            FilterResult result = filterData(config, inputList, cmpContext);

            // 固定字段输出
            outputFilterResult(result, cmpContext);

        } catch (LiteFlowCmpException e) {
            throw e;
        } catch (Exception e) {
            throw LiteFlowExceptionHelper.createNodeException(
                    e,
                    LiteFlowErrorCode.CMP_EXECUTION_FAILED,
                    name + "执行失败: " + e.getMessage(),
                    this
            );
        }
    }

    /**
     * 验证组件必需配置
     *
     * @param config 组件配置
     */
    public void checkConfig(JSONObject config) {
        List<String> paramList = new ArrayList<>();
        if (ObjectUtil.isEmpty(config)) {
            paramList.add("inputDataPath");
            paramList.add("condition");
        } else {
            if (!config.containsKey("inputDataPath") && ObjectUtil.isEmpty(config.get("inputDataPath"))) {
                paramList.add("inputDataPath");
            }
            if (!config.containsKey("condition") && ObjectUtil.isEmpty(config.get("condition"))) {
                paramList.add("condition");
            }
        }

        if (paramList.isEmpty()) {
            return;
        }

        throw LiteFlowExceptionHelper.createNodeException(
                LiteFlowErrorCode.CMP_CONFIG_VALIDATION_FAILED,
                LiteFlowErrorCode.CMP_CONFIG_VALIDATION_FAILED.getDesc() + "：缺少" + CollUtil.join(paramList, "、") + "配置",
                this
        );
    }

    /**
     * 输出过滤结果
     *
     * @param result  过滤结果
     * @param context LiteFlow上下文
     */
    public void outputFilterResult(FilterResult result, DefaultContext context) {
        ContextHelper.setContextData(this.getTag(), "matchedData", result.getMatchedData(), context);
        ContextHelper.setContextData(this.getTag(), "unmatchedData", result.getUnmatchedData(), context);
    }

    /**
     * 转换为列表
     *
     * @param data 输入数据
     * @return 列表
     */
    @SuppressWarnings("unchecked")
    private List<Object> convertToList(Object data) {
        if (data == null) {
            return new ArrayList<>();
        }

        if (data instanceof List) {
            return (List<Object>) data;
        }

        List<Object> result = new ArrayList<>();
        result.add(data);
        return result;
    }

    /**
     * 过滤数据（使用工具类）
     *
     * @param config    节点配置
     * @param inputList 输入数据列表
     * @param context   LiteFlow上下文
     * @return 过滤结果
     */
    private FilterResult filterData(JSONObject config, List<Object> inputList, DefaultContext context) {
        FilterResult result = new FilterResult();

        String condition = config.getStr("condition");

        for (Object record : inputList) {
            // 使用SpEL 表达式工具类评估条件
            boolean matches = evaluateConditions(condition, record, context);

            if (matches) {
                result.getMatchedData().add(record);
            } else {
                result.getUnmatchedData().add(record);
            }
        }
        return result;
    }

    /**
     * 评估条件配置（SpEL 表达式）
     *
     * @param condition 条件表达式(带点的参数从context中获取，不带点的参数就是inputData的属性)
     * @param inputData 输入数据
     * @param context   LiteFlow上下文
     * @return 评估结果
     */
    public static boolean evaluateConditions(String condition, Object inputData, DefaultContext context) {
        // TODO 待完善
        return true;
    }


}
