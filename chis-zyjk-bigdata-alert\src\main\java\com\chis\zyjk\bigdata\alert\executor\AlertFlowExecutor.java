package com.chis.zyjk.bigdata.alert.executor;

import com.chis.zyjk.bigdata.alert.liteflow.pojo.dto.RuleExecutionResult;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertCmpContext;
import com.chis.zyjk.bigdata.alert.pojo.context.AlertGlobalContext;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import com.yomahub.liteflow.slot.DefaultContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * LiteFlow规则执行器
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AlertFlowExecutor {

    @Resource
    private FlowExecutor flowExecutor;

    /**
     * 执行LiteFlow规则（纯动态配置模式）
     *
     * @param globalContext 预警全局上下文
     * @param cmpContext    预警组件上下文
     * @return 执行结果
     */
    public RuleExecutionResult executeRule(AlertGlobalContext globalContext, AlertCmpContext cmpContext) {
        log.info("开始执行预警{}", globalContext.toString());


    }

    /**
     * 初始化执行上下文
     *
     * @param ruleCode 规则编码
     * @return 执行上下文
     */
    private DefaultContext initializeContext(String ruleCode) {
        return new DefaultContext();
    }

    /**
     * 处理执行结果
     *
     * @param response LiteFlow响应
     * @param result   执行结果
     */
    private void processExecutionResult(LiteflowResponse response, RuleExecutionResult result) {
    }


}
