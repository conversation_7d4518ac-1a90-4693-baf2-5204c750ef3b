package com.chis.zyjk.bigdata.alert.liteflow.utils;

import com.chis.project.frame.common.tools.json.JSONObject;
import com.yomahub.liteflow.slot.DefaultContext;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 变量替换工具类
 * <p>
 * 支持的变量语法：
 * - ${global.field} - 引用全局上下文数据
 * - ${nodeId.currentItem.field} - 引用指定节点的当前项数据
 * - ${nodeId.currentData} - 引用指定节点的当前批次数据
 * - ${nodeId.loopIndex} - 引用指定节点的循环索引
 * - ${uuid()} - 生成UUID
 * - ${timestamp()} - 生成时间戳
 * - ${date('yyyy-MM-dd')} - 格式化当前日期
 *
 * <AUTHOR> Assistant
 * @since 2024-01-17
 */
@Slf4j
public final class VariableReplacerUtils {

    // 变量表达式正则模式
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^}]+)\\}");

    // 函数调用正则模式
    private static final Pattern FUNCTION_PATTERN = Pattern.compile("(\\w+)\\(([^)]*)\\)");

    /**
     * 替换字符串中的所有变量
     *
     * @param template 包含变量的模板字符串
     * @param context  LiteFlow上下文
     * @return 替换后的字符串
     */
    public static String replaceVariables(String template, DefaultContext context) {
        if (template == null || template.isEmpty()) {
            return template;
        }

        Matcher matcher = VARIABLE_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String expression = matcher.group(1);
            String value = resolveExpression(expression, context);
            matcher.appendReplacement(result, Matcher.quoteReplacement(value));
        }

        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 替换Map中的所有变量
     *
     * @param map     包含变量的Map
     * @param context LiteFlow上下文
     * @return 替换后的Map
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> replaceVariables(Map<String, Object> map, DefaultContext context) {
        if (map == null || map.isEmpty()) {
            return map;
        }

        Map<String, Object> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof String) {
                result.put(key, replaceVariables((String) value, context));
            } else if (value instanceof Map) {
                result.put(key, replaceVariables((Map<String, Object>) value, context));
            } else {
                result.put(key, value);
            }
        }

        return result;
    }

    /**
     * 替换JSONObject中的所有变量
     *
     * @param json    包含变量的JSONObject
     * @param context LiteFlow上下文
     * @return 替换后的JSONObject
     */
    public static JSONObject replaceVariables(JSONObject json, DefaultContext context) {
        if (json == null || json.isEmpty()) {
            return json;
        }

        JSONObject result = new JSONObject();
        for (String key : json.keySet()) {
            Object value = json.get(key);

            if (value instanceof String) {
                result.set(key, replaceVariables((String) value, context));
            } else if (value instanceof JSONObject) {
                result.set(key, replaceVariables((JSONObject) value, context));
            } else {
                result.set(key, value);
            }
        }

        return result;
    }

    /**
     * 解析单个表达式
     *
     * @param expression 表达式内容（不包含${}）
     * @param context    LiteFlow上下文
     * @return 解析后的值
     */
    private static String resolveExpression(String expression, DefaultContext context) {
        try {
            // 检查是否是函数调用
            Matcher functionMatcher = FUNCTION_PATTERN.matcher(expression);
            if (functionMatcher.matches()) {
                String functionName = functionMatcher.group(1);
                String params = functionMatcher.group(2);
                return callFunction(functionName, params);
            }

            // 解析路径表达式：global.field 或 nodeId.currentItem.field
            String[] parts = expression.split("\\.");
            if (parts.length < 2) {
                System.err.println("无效的变量表达式: " + expression);
                return "${" + expression + "}"; // 返回原始表达式
            }

            String scope = parts[0]; // global 或 nodeId
            String path = String.join(".", java.util.Arrays.copyOfRange(parts, 1, parts.length));

            if ("global".equals(scope)) {
                return getGlobalValue(path, context);
            } else {
                return getNodeValue(scope, path, context);
            }

        } catch (Exception e) {
            System.err.println("解析变量表达式失败: " + expression + ", " + e.getMessage());
            return "${" + expression + "}"; // 返回原始表达式
        }
    }

    /**
     * 获取全局上下文值
     */
    private static String getGlobalValue(String path, DefaultContext context) {
        Object globalData = context.getData("global");
        if (globalData instanceof JSONObject) {
            JSONObject global = (JSONObject) globalData;
            Object value = getNestedValue(global, path);
            return value != null ? value.toString() : "";
        }
        return "";
    }

    /**
     * 获取节点上下文值
     */
    private static String getNodeValue(String nodeId, String path, DefaultContext context) {
        Object nodesData = context.getData("nodes");
        if (nodesData instanceof JSONObject) {
            JSONObject nodes = (JSONObject) nodesData;
            JSONObject nodeData = nodes.getJSONObject(nodeId);
            if (nodeData != null) {
                Object value = getNestedValue(nodeData, path);
                return value != null ? value.toString() : "";
            }
        }
        return "";
    }

    /**
     * 获取嵌套路径的值
     */
    public static Object getNestedValue(JSONObject json, String path) {
        String[] parts = path.split("\\.");
        Object current = json;

        for (String part : parts) {
            if (current instanceof JSONObject) {
                current = ((JSONObject) current).get(part);
            } else {
                return null;
            }
        }

        return current;
    }

    /**
     * 调用内置函数
     */
    private static String callFunction(String functionName, String params) {
        switch (functionName) {
            case "uuid":
                return UUID.randomUUID().toString();
            case "timestamp":
                return String.valueOf(System.currentTimeMillis());
            case "date":
                String format = params.replaceAll("'", "");
                if (format.isEmpty()) {
                    format = "yyyy-MM-dd HH:mm:ss";
                }
                return LocalDateTime.now().format(DateTimeFormatter.ofPattern(format));
            default:
                System.err.println("未知的函数: " + functionName);
                return "${" + functionName + "(" + params + ")}";
        }
    }
}
