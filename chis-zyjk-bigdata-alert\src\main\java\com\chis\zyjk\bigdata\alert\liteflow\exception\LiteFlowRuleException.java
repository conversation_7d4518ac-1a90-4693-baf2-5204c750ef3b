package com.chis.zyjk.bigdata.alert.liteflow.exception;

import com.yomahub.liteflow.exception.LiteFlowException;
import lombok.Getter;

/**
 * LiteFlow节点级别异常
 */
@Getter
public class LiteFlowRuleException extends LiteFlowException {

    /**
     * 规则编码
     */
    private final String ruleCode;

    /**
     * 构造函数 - 使用错误码枚举和规则编码
     *
     * @param errorCode 错误码枚举
     * @param ruleCode  规则编码
     */
    public LiteFlowRuleException(LiteFlowErrorCode errorCode, String ruleCode) {
        this(errorCode, errorCode.getDesc(), ruleCode);
    }

    /**
     * 构造函数 - 使用错误码枚举和自定义消息和规则编码
     *
     * @param errorCode 错误码枚举
     * @param message   自定义消息
     * @param ruleCode  规则编码
     */
    public LiteFlowRuleException(LiteFlowErrorCode errorCode, String message, String ruleCode) {
        super(errorCode.getCode(), message);
        this.ruleCode = ruleCode;
    }

    /**
     * 构造函数 - 使用错误码枚举和规则编码和原因异常
     *
     * @param errorCode 错误码枚举
     * @param ruleCode  规则编码
     * @param cause     原因异常
     */
    public LiteFlowRuleException(LiteFlowErrorCode errorCode, String ruleCode, Throwable cause) {
        this(errorCode, errorCode.getDesc(), ruleCode, cause);
    }

    /**
     * 构造函数 - 使用错误码枚举和自定义消息和规则编码和原因异常
     *
     * @param errorCode 错误码枚举
     * @param message   自定义消息
     * @param ruleCode  规则编码
     * @param cause     原因异常
     */
    public LiteFlowRuleException(LiteFlowErrorCode errorCode, String message, String ruleCode, Throwable cause) {
        super(errorCode.getCode(), message, cause);
        this.ruleCode = ruleCode;
    }

    /**
     * 获取详细的异常信息
     *
     * @return 详细异常信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append("LiteFlow规则异常 [").append(super.getCode()).append("]: ").append(getMessage());

        if (ruleCode != null) {
            sb.append(", 规则编码: ").append(ruleCode);
        }

        return sb.toString();
    }

    @Override
    public String toString() {
        return getDetailedMessage();
    }
}
