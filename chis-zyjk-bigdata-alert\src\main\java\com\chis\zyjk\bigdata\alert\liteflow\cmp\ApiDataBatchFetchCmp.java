package com.chis.zyjk.bigdata.alert.liteflow.cmp;

import com.chis.project.frame.common.tools.core.collection.CollUtil;
import com.chis.project.frame.common.tools.core.util.ObjectUtil;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.bigdata.alert.liteflow.common.ContextHelper;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowCmpException;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowErrorCode;
import com.chis.zyjk.bigdata.alert.liteflow.exception.LiteFlowExceptionHelper;
import com.chis.zyjk.bigdata.alert.liteflow.pojo.context.CmpContext;
import com.chis.zyjk.bigdata.alert.liteflow.utils.BaseApiNodeUtils;
import com.chis.zyjk.bigdata.alert.liteflow.utils.VariableReplacerUtils;
import com.yomahub.liteflow.annotation.LiteflowComponent;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.core.NodeBooleanComponent;
import com.yomahub.liteflow.log.LFLog;
import com.yomahub.liteflow.log.LFLoggerManager;
import com.yomahub.liteflow.slot.DefaultContext;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * API数据分批获取组件（WHILE条件组件）
 * <p></p>
 * <b>WHILE(apiDataBatchFetch).DO(...)</b>
 */
@LiteflowComponent(id = "apiDataBatchFetch", name = "API数据分批获取组件")
public class ApiDataBatchFetchCmp extends NodeBooleanComponent {
    private final LFLog logger = LFLoggerManager.getLogger(FlowExecutor.class);

    @Override
    public boolean processBoolean() {
        String nodeId = this.getNodeId();
        String name = this.getName();
        String tag = this.getTag();
        if (ObjectUtil.isEmpty(tag)) {
            throw LiteFlowExceptionHelper.createNodeException(LiteFlowErrorCode.CMP_TAG_MISSING, this);
        }

        // logger.info("开始执行API数据分批获取组件，nodeId: {}, tag: {}", nodeId, tag);

        try {
            CmpContext cmpContext = this.getContextBean(CmpContext.class);
            // 上下文中获取组件配置
            JSONObject config = ContextHelper.getOrCreateContextConfig(cmpContext, tag);
            checkConfig(config);

            JSONObject data = ContextHelper.getOrCreateContextData(cmpContext, tag);

            Integer currentPage = data.getInt("currentPage");
            Integer batchCount = data.getInt("batchCount");
            if (currentPage == null) {
                currentPage = 1;
            }
            if (batchCount == null) {
                batchCount = 0;
            }

            // 检查是否超过最大批次数
            JSONObject batchConfig = config.getJSONObject("batchConfig");
            int maxBatches = batchConfig.getInt("maxBatches", 100);
            if (batchCount >= maxBatches) {
                logger.info("已达到最大批次数限制，停止循环，nodeId: {}, tag: {}, batchCount: {}, maxBatches: {}",
                        nodeId, tag, batchCount, maxBatches);
                return false;
            }

            Boolean lastHasMore = data.getBool("lastHasMore");
            if (lastHasMore != null && !lastHasMore) {
                logger.debug("上次已判断无更多数据，结束循环，tag: {}", tag);
                return false;
            }

            // 获取当前页数据
            List<Object> dataList = fetchSingleBatch(nodeId, config, currentPage);

            // 更新数据到上下文供其他节点使用
            BaseApiNodeUtils.updateCurrentDataToNodeHelper(tag, dataList, cmpContext);

            // 判断是否继续循环（支持自定义停止条件）
            boolean hasMore = shouldContinueLoop(dataList, config, data, tag, currentPage);

            // 更新批次计数和页码到两级结构
            data.set("batchCount", batchCount + 1);
            if (hasMore) {
                // 更新页码为下一页
                data.set("currentPage", currentPage + 1);
            }

            // logger.info("API数据单次获取节点执行完成，nodeId: {}, tag: {}, dataSize: {}", nodeId, tag, data.size());

            return hasMore;
        } catch (LiteFlowCmpException e) {
            throw e;
        } catch (Exception e) {
            throw LiteFlowExceptionHelper.createNodeException(
                    e,
                    LiteFlowErrorCode.CMP_EXECUTION_FAILED,
                    name + "执行失败: " + e.getMessage(),
                    this
            );
        }
    }

    /**
     * 验证组件必需配置
     *
     * @param config 组件配置
     */
    public void checkConfig(JSONObject config) {
        List<String> paramList = new ArrayList<>();
        if (ObjectUtil.isEmpty(config)) {
            paramList.add("apiUrl");
            paramList.add("batchConfig");
        } else {
            if (!config.containsKey("apiUrl") && ObjectUtil.isEmpty(config.get("apiUrl"))) {
                paramList.add("apiUrl");
            }
            if (!config.containsKey("batchConfig") && ObjectUtil.isEmpty(config.get("batchConfig"))) {
                paramList.add("batchConfig");
            }
        }

        if (paramList.isEmpty()) {
            return;
        }

        throw LiteFlowExceptionHelper.createNodeException(
                LiteFlowErrorCode.CMP_CONFIG_VALIDATION_FAILED,
                LiteFlowErrorCode.CMP_CONFIG_VALIDATION_FAILED.getDesc() + "：缺少" + CollUtil.join(paramList, "、") + "配置",
                this
        );
    }

    /**
     * 获取单批次数据
     */
    private List<Object> fetchSingleBatch(String nodeId, JSONObject config, int pageNum) {
        // 获取上下文进行变量替换
        DefaultContext context = this.getContextBean(DefaultContext.class);

        // 构建API请求参数（支持变量替换）
        Map<String, Object> params = BaseApiNodeUtils.buildApiParams(config, pageNum, context);

        // 构建请求头（支持变量替换）
        Map<String, String> headers = BaseApiNodeUtils.buildApiHeaders(config, context);

        String apiUrl = config.getStr("apiUrl");
        String method = config.getStr("method", "POST");
        int timeout = config.getInt("timeout", 30000);

        logger.debug("调用API获取数据，nodeId: {}, url: {}, page: {}", nodeId, apiUrl, pageNum);

        // 执行API调用
        String responseBody = BaseApiNodeUtils.executeApiCall(apiUrl, method, params, headers, timeout, this.getTag());

        // 保存原始响应数据用于总数判断（使用两级结构）
        BaseApiNodeUtils.saveResponseToContext(context, this.getTag(), responseBody);

        // 提取结果数据
        return BaseApiNodeUtils.extractResultData(responseBody, config);
    }

    /**
     * 判断是否应该继续循环
     * <p>
     * 逻辑说明：
     * 1. 如果当前获取到了数据，就返回true让DO执行
     * 2. 同时根据数据量判断下次是否还有数据
     * 3. 将"下次是否有数据"的判断结果保存到上下文
     * 4. 下次调用时，先检查上次的判断结果
     */
    private boolean shouldContinueLoop(List<Object> dataList, JSONObject config, JSONObject data, String contextKey, int currentPage) {
        JSONObject batchConfig = config.getJSONObject("batchConfig");
        if (batchConfig == null) {
            return false;
        }

        // 获取停止条件类型
        String stopCondition = batchConfig.getStr("stopCondition", "PAGE_SIZE");

        // 数据为空时总是停止
        if (dataList == null || dataList.isEmpty()) {
            logger.debug("当前数据为空，结束循环，tag: {}", contextKey);
            return false;
        }

        // 当前有数据，先返回true让DO执行，同时判断下次是否还有数据
        boolean nextHasMore;

        switch (stopCondition) {
            case "PAGE_SIZE":
                // 数据量大于等于页大小时，下次可能还有数据
                int pageSize = batchConfig.getInt("pageSize", 1000);
                nextHasMore = dataList.size() >= pageSize;
                logger.debug("PAGE_SIZE判断: dataSize={}, pageSize={}, nextHasMore={}, tag={}",
                        dataList.size(), pageSize, nextHasMore, contextKey);
                break;

            case "TOTAL_COUNT":
                // 根据总数计算下次是否还有数据
                nextHasMore = shouldContinueByTotalCount(dataList, config, data, contextKey, currentPage);
                break;

            default:
                // 默认使用页大小判断
                int defaultPageSize = batchConfig.getInt("pageSize", 1000);
                nextHasMore = dataList.size() >= defaultPageSize;
                logger.debug("默认判断: dataSize={}, pageSize={}, nextHasMore={}, tag={}",
                        dataList.size(), defaultPageSize, nextHasMore, contextKey);
                break;
        }

        // 保存下次是否有数据的判断结果到两级结构
        data.set("lastHasMore", nextHasMore);

        // 当前有数据，返回true让DO执行
        logger.debug("当前有数据({})，返回true让DO执行，nextHasMore={}, tag={}",
                dataList.size(), nextHasMore, contextKey);
        return true;
    }

    /**
     * 根据总数判断是否继续循环
     */
    private boolean shouldContinueByTotalCount(List<Object> dataList, JSONObject config, JSONObject data, String contextKey, int currentPage) {
        try {
            // 每次都从最新的响应数据中获取总数
            String totalField = config.getJSONObject("batchConfig").getStr("totalField", "total");

            // 从当前响应的原始数据中获取总数
            Object responseObj = data.get("lastResponse");

            int total = 0;
            if (responseObj instanceof JSONObject) {
                JSONObject response = (JSONObject) responseObj;
                Object totalObj = VariableReplacerUtils.getNestedValue(response, totalField);
                if (totalObj instanceof Number) {
                    total = ((Number) totalObj).intValue();
                }
            }

            if (total > 0) {
                int pageSize = config.getJSONObject("batchConfig").getInt("pageSize", 1000);
                int processedCount = (currentPage - 1) * pageSize + (dataList != null ? dataList.size() : 0);

                logger.debug("总数判断: total={}, processedCount={}, currentPage={}, tag={}", total, processedCount, currentPage, contextKey);
                return processedCount < total;
            }

            return false;
        } catch (Exception e) {
            logger.warn("根据总数判断循环条件失败，tag: {}, error: {}", contextKey, e.getMessage());
            return false;
        }
    }

}
