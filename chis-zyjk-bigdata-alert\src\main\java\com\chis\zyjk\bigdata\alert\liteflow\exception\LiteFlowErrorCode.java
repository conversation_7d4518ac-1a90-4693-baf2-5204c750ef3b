package com.chis.zyjk.bigdata.alert.liteflow.exception;

import com.chis.zyjk.core.common.enums.base.StringCodeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * LiteFlow错误码枚举
 * <p>
 * 定义LiteFlow规则引擎相关的错误码
 * <p>
 * 错误码规则：
 * <p>
 * - <b>LF-R-xxx</b>: 规则级别错误
 * <p>
 * - <b>LF-C-xxx</b>: 组件级别错误
 */
@Getter
@AllArgsConstructor
public enum LiteFlowErrorCode implements StringCodeEnum {

    // ==================== 规则级别错误 ====================

    /**
     * 规则表达式为空
     */
    RULE_EXPRESSION_EMPTY("LF-R-001", "规则表达式为空"),

    /**
     * 规则表达式语法错误
     */
    RULE_EXPRESSION_SYNTAX_ERROR("LF-R-002", "规则表达式语法错误"),

    /**
     * 规则组件配置为空
     */
    RULE_CMP_CONFIG_EMPTY("LF-R-003", "规则组件配置为空"),

    /**
     * 规则执行超时
     */
    RULE_EXECUTION_TIMEOUT("LF-R-004", "规则执行超时"),

    /**
     * 规则执行失败
     */
    RULE_EXECUTION_FAILED("LF-R-005", "规则执行失败"),

    /**
     * 规则链未找到
     */
    RULE_CHAIN_NOT_FOUND("LF-R-006", "规则链未找到"),

    // ==================== 组件级别错误 ====================

    /**
     * 组件配置验证失败
     */
    CMP_CONFIG_VALIDATION_FAILED("LF-C-001", "组件配置验证失败"),

    /**
     * 组件执行失败
     */
    CMP_EXECUTION_FAILED("LF-C-002", "组件执行失败"),

    /**
     * 组件类型错误
     */
    CMP_TYPE_ERROR("LF-C-003", "组件类型错误"),

    /**
     * 组件数据获取失败
     */
    CMP_DATA_FETCH_FAILED("LF-C-004", "组件数据获取失败"),

    /**
     * 组件API调用失败
     */
    CMP_API_CALL_FAILED("LF-C-005", "组件API调用失败"),

    /**
     * 组件数据处理失败
     */
    CMP_DATA_PROCESS_FAILED("LF-C-006", "组件数据处理失败"),

    /**
     * 组件TAG缺失
     */
    CMP_TAG_MISSING("LF-C-007", "组件TAG缺失"),

    /**
     * 组件参数错误
     */
    CMP_PARAMETER_ERROR("LF-C-008", "组件参数错误");

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;

    @Override
    public String getEnumDesc() {
        return "LiteFlow错误码";
    }
}
